using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;

namespace AirMonitor.Core;

/// <summary>
/// ViewModel基类，提供通用功能
/// </summary>
public abstract partial class BaseViewModel : ObservableObject
{
    protected readonly ILogger _logger;

    protected BaseViewModel(ILogger logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    [ObservableProperty]
    private bool _isBusy;

    [ObservableProperty]
    private string _title = string.Empty;

    [ObservableProperty]
    private string _statusMessage = string.Empty;

    /// <summary>
    /// 执行异步操作的通用方法
    /// </summary>
    /// <param name="operation">要执行的异步操作</param>
    /// <param name="loadingMessage">加载时显示的消息</param>
    /// <param name="successMessage">成功时显示的消息</param>
    /// <param name="showSuccessMessage">是否显示成功消息</param>
    protected async Task ExecuteAsync(Func<Task> operation, string loadingMessage = "处理中...", 
        string successMessage = "操作完成", bool showSuccessMessage = false)
    {
        if (IsBusy)
            return;

        try
        {
            IsBusy = true;
            StatusMessage = loadingMessage;

            await operation();

            if (showSuccessMessage)
            {
                StatusMessage = successMessage;
            }
            else
            {
                StatusMessage = string.Empty;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行操作时发生错误");
            StatusMessage = $"错误: {ex.Message}";
        }
        finally
        {
            IsBusy = false;
        }
    }

    /// <summary>
    /// 执行带返回值的异步操作
    /// </summary>
    /// <typeparam name="T">返回值类型</typeparam>
    /// <param name="operation">要执行的异步操作</param>
    /// <param name="loadingMessage">加载时显示的消息</param>
    /// <param name="successMessage">成功时显示的消息</param>
    /// <param name="showSuccessMessage">是否显示成功消息</param>
    /// <returns>操作结果</returns>
    protected async Task<T?> ExecuteAsync<T>(Func<Task<T>> operation, string loadingMessage = "处理中...", 
        string successMessage = "操作完成", bool showSuccessMessage = false)
    {
        if (IsBusy)
            return default;

        try
        {
            IsBusy = true;
            StatusMessage = loadingMessage;

            var result = await operation();

            if (showSuccessMessage)
            {
                StatusMessage = successMessage;
            }
            else
            {
                StatusMessage = string.Empty;
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行操作时发生错误");
            StatusMessage = $"错误: {ex.Message}";
            return default;
        }
        finally
        {
            IsBusy = false;
        }
    }

    /// <summary>
    /// 清除状态消息
    /// </summary>
    [RelayCommand]
    protected virtual void ClearStatus()
    {
        StatusMessage = string.Empty;
    }

    /// <summary>
    /// 刷新数据
    /// </summary>
    [RelayCommand]
    protected virtual async Task RefreshAsync()
    {
        await OnRefreshAsync();
    }

    /// <summary>
    /// 子类重写此方法实现具体的刷新逻辑
    /// </summary>
    protected virtual Task OnRefreshAsync()
    {
        return Task.CompletedTask;
    }
}
