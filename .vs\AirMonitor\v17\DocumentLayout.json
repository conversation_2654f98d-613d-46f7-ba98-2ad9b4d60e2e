{"Version": 1, "WorkspaceRootPath": "D:\\01 AirMonitor\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|d:\\01 airmonitor\\airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|d:\\01 airmonitor\\airmonitor\\core\\loggingextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\core\\loggingextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|d:\\01 airmonitor\\airmonitor\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|d:\\01 airmonitor\\airmonitor\\services\\configurationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\services\\configurationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|d:\\01 airmonitor\\airmonitor\\services\\dialogservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\services\\dialogservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|d:\\01 airmonitor\\airmonitor\\core\\baseviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{FFCDBA09-2229-4658-B8A4-EA919C493D3D}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\core\\baseviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 8, "Children": [{"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d84ee353-0bef-5a41-a649-8f89aca5d84d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\01 AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeDocumentMoniker": "AirMonitor\\MainWindow.xaml", "ToolTip": "D:\\01 AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeToolTip": "AirMonitor\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T02:22:14.704Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ConfigurationService.cs", "DocumentMoniker": "D:\\01 AirMonitor\\AirMonitor\\Services\\ConfigurationService.cs", "RelativeDocumentMoniker": "AirMonitor\\Services\\ConfigurationService.cs", "ToolTip": "D:\\01 AirMonitor\\AirMonitor\\Services\\ConfigurationService.cs", "RelativeToolTip": "AirMonitor\\Services\\ConfigurationService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T02:21:39.163Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "DialogService.cs", "DocumentMoniker": "D:\\01 AirMonitor\\AirMonitor\\Services\\DialogService.cs", "RelativeDocumentMoniker": "AirMonitor\\Services\\DialogService.cs", "ToolTip": "D:\\01 AirMonitor\\AirMonitor\\Services\\DialogService.cs", "RelativeToolTip": "AirMonitor\\Services\\DialogService.cs", "ViewState": "AgIAAFsAAAAAAAAAAAAcwGwAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T02:21:30.715Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "LoggingExtensions.cs", "DocumentMoniker": "D:\\01 AirMonitor\\AirMonitor\\Core\\LoggingExtensions.cs", "RelativeDocumentMoniker": "AirMonitor\\Core\\LoggingExtensions.cs", "ToolTip": "D:\\01 AirMonitor\\AirMonitor\\Core\\LoggingExtensions.cs", "RelativeToolTip": "AirMonitor\\Core\\LoggingExtensions.cs", "ViewState": "AgIAACUAAAAAAAAAAAAAADYAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T02:21:08.141Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "App.xaml.cs", "DocumentMoniker": "D:\\01 AirMonitor\\AirMonitor\\App.xaml.cs", "RelativeDocumentMoniker": "AirMonitor\\App.xaml.cs", "ToolTip": "D:\\01 AirMonitor\\AirMonitor\\App.xaml.cs", "RelativeToolTip": "AirMonitor\\App.xaml.cs", "ViewState": "AgIAAEcAAAAAAAAAAAAcwFcAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T02:20:56.179Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "BaseViewModel.cs", "DocumentMoniker": "D:\\01 AirMonitor\\AirMonitor\\Core\\BaseViewModel.cs", "RelativeDocumentMoniker": "AirMonitor\\Core\\BaseViewModel.cs", "ToolTip": "D:\\01 AirMonitor\\AirMonitor\\Core\\BaseViewModel.cs", "RelativeToolTip": "AirMonitor\\Core\\BaseViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T02:16:24.691Z", "EditorCaption": ""}]}]}]}