using Microsoft.Extensions.DependencyInjection;

namespace AirMonitor.Core;

/// <summary>
/// ViewModel定位器，用于在XAML中获取ViewModel实例
/// </summary>
public class ViewModelLocator
{
    private static IServiceProvider? _serviceProvider;

    /// <summary>
    /// 设置服务提供者
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    public static void SetServiceProvider(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    /// <summary>
    /// 获取服务实例
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <returns>服务实例</returns>
    public static T GetService<T>() where T : class
    {
        if (_serviceProvider == null)
            throw new InvalidOperationException("服务提供者未初始化。请先调用SetServiceProvider方法。");

        return _serviceProvider.GetRequiredService<T>();
    }

    /// <summary>
    /// 尝试获取服务实例
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <returns>服务实例，如果不存在则返回null</returns>
    public static T? TryGetService<T>() where T : class
    {
        if (_serviceProvider == null)
            return null;

        return _serviceProvider.GetService<T>();
    }

    // 以下属性用于在XAML中直接绑定ViewModel
    // 示例：DataContext="{Binding Source={x:Static core:ViewModelLocator.MainViewModel}}"

    /// <summary>
    /// 主窗口ViewModel
    /// </summary>
    public static object? MainViewModel => TryGetService<ViewModels.MainViewModel>();

    // 可以根据需要添加更多ViewModel属性
    // public static object? SettingsViewModel => TryGetService<ViewModels.SettingsViewModel>();
    // public static object? DataViewModel => TryGetService<ViewModels.DataViewModel>();
}
