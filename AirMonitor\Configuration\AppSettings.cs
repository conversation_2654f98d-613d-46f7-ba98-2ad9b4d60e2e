namespace AirMonitor.Configuration;

/// <summary>
/// 应用程序配置模型
/// </summary>
public class AppSettings
{
    public LoggingSettings Logging { get; set; } = new();
    public ApplicationSettings Application { get; set; } = new();
    public DatabaseSettings Database { get; set; } = new();
    public UISettings UI { get; set; } = new();
}

/// <summary>
/// 日志配置
/// </summary>
public class LoggingSettings
{
    public LogLevelSettings LogLevel { get; set; } = new();
    public FileLogSettings File { get; set; } = new();
}

/// <summary>
/// 日志级别配置
/// </summary>
public class LogLevelSettings
{
    public string Default { get; set; } = "Information";
    public string Microsoft { get; set; } = "Warning";
    public string MicrosoftHostingLifetime { get; set; } = "Information";
}

/// <summary>
/// 文件日志配置
/// </summary>
public class FileLogSettings
{
    public string Path { get; set; } = "Logs/app-.log";
    public string RollingInterval { get; set; } = "Day";
    public int RetainedFileCountLimit { get; set; } = 30;
    public long FileSizeLimitBytes { get; set; } = 10485760; // 10MB
    public bool RollOnFileSizeLimit { get; set; } = true;
}

/// <summary>
/// 应用程序基本配置
/// </summary>
public class ApplicationSettings
{
    public string Name { get; set; } = "AirMonitor";
    public string Version { get; set; } = "1.0.0";
    public string Environment { get; set; } = "Development";
}

/// <summary>
/// 数据库配置
/// </summary>
public class DatabaseSettings
{
    public string ConnectionString { get; set; } = "Data Source=airmonitor.db";
    public string Provider { get; set; } = "SQLite";
}

/// <summary>
/// UI配置
/// </summary>
public class UISettings
{
    public string Theme { get; set; } = "Light";
    public string Language { get; set; } = "zh-CN";
    public bool AutoSave { get; set; } = true;
    public int AutoSaveInterval { get; set; } = 300; // 秒
}
