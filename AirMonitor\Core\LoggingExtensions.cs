using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Events;
using System.IO;

namespace AirMonitor.Core;

/// <summary>
/// 日志配置扩展方法
/// </summary>
public static class LoggingExtensions
{
    /// <summary>
    /// 配置Serilog日志
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSerilogLogging(this IServiceCollection services, IConfiguration configuration)
    {
        // 确保日志目录存在
        var logPath = configuration["Logging:File:Path"] ?? "Logs/app-.log";
        var logDirectory = Path.GetDirectoryName(logPath);
        if (!string.IsNullOrEmpty(logDirectory) && !Directory.Exists(logDirectory))
        {
            Directory.CreateDirectory(logDirectory);
        }

        // 配置Serilog - 专为WPF应用程序优化
        var loggerConfig = new LoggerConfiguration()
            .ReadFrom.Configuration(configuration)
            .Enrich.FromLogContext()
            .Enrich.WithThreadId()
            .Enrich.WithMachineName()
            .Enrich.WithProperty("Application", "AirMonitor")
            .WriteTo.File(
                path: logPath,
                rollingInterval: GetRollingInterval(configuration["Logging:File:RollingInterval"]),
                retainedFileCountLimit: GetRetainedFileCountLimit(configuration["Logging:File:RetainedFileCountLimit"]),
                fileSizeLimitBytes: GetFileSizeLimitBytes(configuration["Logging:File:FileSizeLimitBytes"]),
                rollOnFileSizeLimit: GetRollOnFileSizeLimit(configuration["Logging:File:RollOnFileSizeLimit"]),
                outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}",
                shared: true) // 允许多个进程写入同一文件
            .MinimumLevel.Is(GetLogLevel(configuration["Logging:LogLevel:Default"]))
            .MinimumLevel.Override("Microsoft", GetLogLevel(configuration["Logging:LogLevel:Microsoft"]))
            .MinimumLevel.Override("Microsoft.Hosting.Lifetime", GetLogLevel(configuration["Logging:LogLevel:MicrosoftHostingLifetime"]));

#if DEBUG
        // 在Debug模式下添加调试输出，可以在Visual Studio输出窗口中查看
        loggerConfig.WriteTo.Debug(outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}");
#endif

        Log.Logger = loggerConfig.CreateLogger();

        // 添加Serilog到服务容器
        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddSerilog(dispose: true);
        });

        return services;
    }

    /// <summary>
    /// 获取日志级别
    /// </summary>
    /// <param name="level">日志级别字符串</param>
    /// <returns>Serilog日志级别</returns>
    private static LogEventLevel GetLogLevel(string? level)
    {
        return level?.ToLowerInvariant() switch
        {
            "trace" => LogEventLevel.Verbose,
            "debug" => LogEventLevel.Debug,
            "information" => LogEventLevel.Information,
            "warning" => LogEventLevel.Warning,
            "error" => LogEventLevel.Error,
            "critical" => LogEventLevel.Fatal,
            _ => LogEventLevel.Information
        };
    }

    /// <summary>
    /// 获取日志滚动间隔
    /// </summary>
    /// <param name="interval">间隔字符串</param>
    /// <returns>滚动间隔</returns>
    private static RollingInterval GetRollingInterval(string? interval)
    {
        return interval?.ToLowerInvariant() switch
        {
            "minute" => RollingInterval.Minute,
            "hour" => RollingInterval.Hour,
            "day" => RollingInterval.Day,
            "month" => RollingInterval.Month,
            "year" => RollingInterval.Year,
            _ => RollingInterval.Day
        };
    }

    /// <summary>
    /// 获取保留文件数量限制
    /// </summary>
    /// <param name="limit">限制字符串</param>
    /// <returns>文件数量限制</returns>
    private static int? GetRetainedFileCountLimit(string? limit)
    {
        if (int.TryParse(limit, out var result))
            return result;
        return 30; // 默认保留30个文件
    }

    /// <summary>
    /// 获取文件大小限制
    /// </summary>
    /// <param name="size">大小字符串</param>
    /// <returns>文件大小限制</returns>
    private static long? GetFileSizeLimitBytes(string? size)
    {
        if (long.TryParse(size, out var result))
            return result;
        return 10485760; // 默认10MB
    }

    /// <summary>
    /// 获取是否按文件大小滚动
    /// </summary>
    /// <param name="roll">滚动字符串</param>
    /// <returns>是否滚动</returns>
    private static bool GetRollOnFileSizeLimit(string? roll)
    {
        if (bool.TryParse(roll, out var result))
            return result;
        return true; // 默认启用
    }
}
