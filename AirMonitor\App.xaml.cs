﻿using AirMonitor.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System.IO;
using System.Windows;

namespace AirMonitor;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    protected override async void OnStartup(StartupEventArgs e)
    {
        try
        {
            // 构建配置
            var configuration = BuildConfiguration();

            // 构建主机
            _host = CreateHostBuilder(configuration).Build();

            // 设置ViewModelLocator的服务提供者
            ViewModelLocator.SetServiceProvider(_host.Services);

            // 启动主机
            await _host.StartAsync();

            // 创建并显示主窗口
            var mainWindow = _host.Services.GetRequiredService<MainWindow>();
            mainWindow.Show();

            base.OnStartup(e);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"应用程序启动失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown(1);
        }
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        try
        {
            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }

            Log.CloseAndFlush();
        }
        catch (Exception ex)
        {
            // 记录关闭时的错误，但不阻止应用程序退出
            System.Diagnostics.Debug.WriteLine($"应用程序关闭时发生错误：{ex.Message}");
        }

        base.OnExit(e);
    }

    private static IConfiguration BuildConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

        return builder.Build();
    }

    private static IHostBuilder CreateHostBuilder(IConfiguration configuration)
    {
        return Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // 注册配置
                services.AddSingleton(configuration);

                // 注册应用程序服务
                services.AddApplication(configuration);
            })
            .UseSerilog();
    }
}
