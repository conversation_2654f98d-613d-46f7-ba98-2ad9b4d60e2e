<Window x:Class="AirMonitor.Services.InputDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="输入" Height="200" Width="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        ShowInTaskbar="False">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <TextBlock x:Name="MessageTextBlock" 
                   Grid.Row="0"
                   TextWrapping="Wrap"
                   VerticalAlignment="Center"/>
        
        <TextBox x:Name="InputTextBox"
                 Grid.Row="2"
                 Height="25"
                 TextChanged="InputTextBox_TextChanged"/>
        
        <StackPanel Grid.Row="4"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right">
            <Button Name="OkButton"
                    Content="确定"
                    Width="75"
                    Height="25"
                    Margin="0,0,10,0"
                    IsDefault="True"
                    Click="OkButton_Click"/>
            <Button Name="CancelButton"
                    Content="取消"
                    Width="75"
                    Height="25"
                    IsCancel="True"
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
