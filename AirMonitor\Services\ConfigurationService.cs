using AirMonitor.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.IO;
using System.Text.Json;

namespace AirMonitor.Services;

/// <summary>
/// 配置服务实现
/// </summary>
public class ConfigurationService : IConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ConfigurationService> _logger;
    private readonly string _configFilePath;
    private AppSettings? _appSettings;

    public ConfigurationService(IConfiguration configuration, ILogger<ConfigurationService> logger)
    {
        _configuration = configuration;
        _logger = logger;
        _configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
        
        LoadAppSettings();
    }

    public AppSettings AppSettings => _appSettings ??= LoadAppSettings();

    public T GetValue<T>(string key, T defaultValue = default!)
    {
        try
        {
            var value = _configuration[key];
            if (value == null)
                return defaultValue;

            if (typeof(T) == typeof(string))
                return (T)(object)value;

            return (T)Convert.ChangeType(value, typeof(T));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取配置值失败，使用默认值。Key: {Key}", key);
            return defaultValue;
        }
    }

    public void SetValue(string key, object value)
    {
        try
        {
            _configuration[key] = value?.ToString();
            _logger.LogInformation("设置配置值: {Key} = {Value}", key, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置配置值失败。Key: {Key}, Value: {Value}", key, value);
            throw;
        }
    }

    public async Task SaveAsync()
    {
        try
        {
            // 这里实现保存配置到文件的逻辑
            // 注意：IConfiguration默认是只读的，如果需要写入，需要使用其他方式
            _logger.LogInformation("保存配置到文件: {FilePath}", _configFilePath);
            
            // 实际项目中可能需要使用专门的配置写入库或自定义实现
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存配置失败");
            throw;
        }
    }

    public async Task ReloadAsync()
    {
        try
        {
            if (_configuration is IConfigurationRoot configRoot)
            {
                configRoot.Reload();
                _appSettings = null; // 重置缓存
                LoadAppSettings();
                _logger.LogInformation("重新加载配置成功");
            }
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新加载配置失败");
            throw;
        }
    }

    public bool ContainsKey(string key)
    {
        return _configuration[key] != null;
    }

    public void RemoveKey(string key)
    {
        try
        {
            _configuration[key] = null;
            _logger.LogInformation("删除配置键: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除配置键失败。Key: {Key}", key);
            throw;
        }
    }

    public IEnumerable<string> GetAllKeys()
    {
        return GetAllKeysRecursive(_configuration.AsEnumerable());
    }

    private AppSettings LoadAppSettings()
    {
        try
        {
            _appSettings = new AppSettings();
            _configuration.Bind(_appSettings);
            _logger.LogInformation("加载应用程序配置成功");
            return _appSettings;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载应用程序配置失败，使用默认配置");
            return new AppSettings();
        }
    }

    private static IEnumerable<string> GetAllKeysRecursive(IEnumerable<KeyValuePair<string, string?>> configItems)
    {
        return configItems
            .Where(item => !string.IsNullOrEmpty(item.Key))
            .Select(item => item.Key)
            .Distinct()
            .OrderBy(key => key);
    }
}
