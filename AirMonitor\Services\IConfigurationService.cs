using AirMonitor.Configuration;

namespace AirMonitor.Services;

/// <summary>
/// 配置服务接口
/// </summary>
public interface IConfigurationService
{
    /// <summary>
    /// 获取应用程序配置
    /// </summary>
    AppSettings AppSettings { get; }

    /// <summary>
    /// 获取配置值
    /// </summary>
    /// <typeparam name="T">配置值类型</typeparam>
    /// <param name="key">配置键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>配置值</returns>
    T GetValue<T>(string key, T defaultValue = default!);

    /// <summary>
    /// 设置配置值
    /// </summary>
    /// <param name="key">配置键</param>
    /// <param name="value">配置值</param>
    void SetValue(string key, object value);

    /// <summary>
    /// 保存配置
    /// </summary>
    Task SaveAsync();

    /// <summary>
    /// 重新加载配置
    /// </summary>
    Task ReloadAsync();

    /// <summary>
    /// 检查配置键是否存在
    /// </summary>
    /// <param name="key">配置键</param>
    /// <returns>是否存在</returns>
    bool ContainsKey(string key);

    /// <summary>
    /// 删除配置键
    /// </summary>
    /// <param name="key">配置键</param>
    void RemoveKey(string key);

    /// <summary>
    /// 获取所有配置键
    /// </summary>
    /// <returns>配置键列表</returns>
    IEnumerable<string> GetAllKeys();
}
