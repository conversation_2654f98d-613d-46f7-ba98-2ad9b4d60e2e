using AirMonitor.Services;
using AirMonitor.ViewModels;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace AirMonitor.Core;

/// <summary>
/// 服务注册扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 注册应用程序服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddApplicationServices(this IServiceCollection services, IConfiguration configuration)
    {
        // 注册配置服务
        services.AddSingleton<IConfigurationService, ConfigurationService>();

        // 注册对话框服务
        services.AddSingleton<IDialogService, DialogService>();

        // 注册日志查看器服务
        services.AddSingleton<ILogViewerService, LogViewerService>();

        // 注册数据服务（示例）
        // services.AddScoped(typeof(IDataService<>), typeof(DataService<>));

        return services;
    }

    /// <summary>
    /// 注册ViewModels
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddViewModels(this IServiceCollection services)
    {
        // 注册ViewModels
        services.AddTransient<MainViewModel>();
        
        // 可以根据需要添加更多ViewModels
        // services.AddTransient<SettingsViewModel>();
        // services.AddTransient<DataViewModel>();

        return services;
    }

    /// <summary>
    /// 注册Views
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddViews(this IServiceCollection services)
    {
        // 注册Views（如果需要通过DI创建）
        services.AddTransient<MainWindow>();
        
        // 可以根据需要添加更多Views
        // services.AddTransient<SettingsWindow>();
        // services.AddTransient<DataWindow>();

        return services;
    }

    /// <summary>
    /// 注册所有应用程序依赖
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddApplication(this IServiceCollection services, IConfiguration configuration)
    {
        // 添加日志
        services.AddSerilogLogging(configuration);

        // 添加应用程序服务
        services.AddApplicationServices(configuration);

        // 添加ViewModels
        services.AddViewModels();

        // 添加Views
        services.AddViews();

        return services;
    }
}
