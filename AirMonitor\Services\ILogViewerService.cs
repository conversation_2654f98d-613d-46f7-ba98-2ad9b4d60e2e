namespace AirMonitor.Services;

/// <summary>
/// 日志查看器服务接口
/// </summary>
public interface ILogViewerService
{
    /// <summary>
    /// 获取最新的日志条目
    /// </summary>
    /// <param name="count">获取的条目数量</param>
    /// <returns>日志条目列表</returns>
    Task<IEnumerable<LogEntry>> GetRecentLogsAsync(int count = 100);

    /// <summary>
    /// 根据日期范围获取日志
    /// </summary>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>日志条目列表</returns>
    Task<IEnumerable<LogEntry>> GetLogsByDateRangeAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// 根据日志级别筛选日志
    /// </summary>
    /// <param name="level">日志级别</param>
    /// <param name="count">获取的条目数量</param>
    /// <returns>日志条目列表</returns>
    Task<IEnumerable<LogEntry>> GetLogsByLevelAsync(LogLevel level, int count = 100);

    /// <summary>
    /// 搜索日志内容
    /// </summary>
    /// <param name="searchText">搜索文本</param>
    /// <param name="count">获取的条目数量</param>
    /// <returns>日志条目列表</returns>
    Task<IEnumerable<LogEntry>> SearchLogsAsync(string searchText, int count = 100);

    /// <summary>
    /// 清理旧日志文件
    /// </summary>
    /// <param name="daysToKeep">保留天数</param>
    /// <returns>清理的文件数量</returns>
    Task<int> CleanupOldLogsAsync(int daysToKeep = 7);

    /// <summary>
    /// 获取日志文件路径
    /// </summary>
    /// <returns>当前日志文件路径</returns>
    string GetCurrentLogFilePath();

    /// <summary>
    /// 获取所有日志文件路径
    /// </summary>
    /// <returns>所有日志文件路径列表</returns>
    Task<IEnumerable<string>> GetAllLogFilePathsAsync();
}

/// <summary>
/// 日志条目
/// </summary>
public class LogEntry
{
    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// 日志级别
    /// </summary>
    public LogLevel Level { get; set; }

    /// <summary>
    /// 来源上下文
    /// </summary>
    public string SourceContext { get; set; } = string.Empty;

    /// <summary>
    /// 日志消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 异常信息
    /// </summary>
    public string? Exception { get; set; }

    /// <summary>
    /// 线程ID
    /// </summary>
    public int? ThreadId { get; set; }

    /// <summary>
    /// 机器名
    /// </summary>
    public string? MachineName { get; set; }
}

/// <summary>
/// 日志级别枚举
/// </summary>
public enum LogLevel
{
    Verbose = 0,
    Debug = 1,
    Information = 2,
    Warning = 3,
    Error = 4,
    Fatal = 5
}
