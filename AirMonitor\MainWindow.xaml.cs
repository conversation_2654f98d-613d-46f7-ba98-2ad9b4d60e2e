using AirMonitor.ViewModels;
using Microsoft.Extensions.Logging;
using System.Windows;
using System.Windows.Threading;

namespace AirMonitor;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private readonly MainViewModel _viewModel;
    private readonly ILogger<MainWindow> _logger;
    private readonly DispatcherTimer _timer;

    public MainWindow(MainViewModel viewModel, ILogger<MainWindow> logger)
    {
        _viewModel = viewModel;
        _logger = logger;

        InitializeComponent();

        // 设置DataContext
        DataContext = _viewModel;

        // 创建定时器来更新时间
        _timer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(1)
        };
        _timer.Tick += Timer_Tick;
        _timer.Start();

        _logger.LogInformation("MainWindow 已初始化");
    }

    private void Timer_Tick(object? sender, EventArgs e)
    {
        _viewModel.UpdateTime();
    }

    protected override void OnClosed(EventArgs e)
    {
        _timer?.Stop();
        _logger.LogInformation("MainWindow 已关闭");
        base.OnClosed(e);
    }
}