<Window x:Class="AirMonitor.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:AirMonitor"
        mc:Ignorable="d"
        Title="{Binding Title}"
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>

        <Style x:Key="InfoTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#34495E"/>
            <Setter Property="Margin" Value="0,5"/>
        </Style>

        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Background" Value="#3498DB"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#2980B9"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#21618C"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BDC3C7"/>
                    <Setter Property="Cursor" Value="Arrow"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="CounterTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="48"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#E74C3C"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题区域 -->
        <TextBlock Grid.Row="0"
                   Text="{Binding WelcomeMessage}"
                   Style="{StaticResource HeaderTextStyle}"
                   HorizontalAlignment="Center"/>

        <!-- 信息区域 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
            <TextBlock Text="当前时间：" Style="{StaticResource InfoTextStyle}"/>
            <TextBlock Text="{Binding CurrentTime}" Style="{StaticResource InfoTextStyle}" FontWeight="Bold"/>
        </StackPanel>

        <!-- 计数器区域 -->
        <Border Grid.Row="2"
                Background="#ECF0F1"
                CornerRadius="10"
                Padding="30"
                Margin="0,0,0,30">
            <StackPanel>
                <TextBlock Text="计数器演示"
                           FontSize="18"
                           FontWeight="Bold"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,20"/>

                <TextBlock Text="{Binding Counter}"
                           Style="{StaticResource CounterTextStyle}"/>

                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                    <Button Content="增加"
                            Command="{Binding IncrementCounterCommand}"
                            Style="{StaticResource ButtonStyle}"/>
                    <Button Content="重置"
                            Command="{Binding ResetCounterCommand}">
                        <Button.Style>
                            <Style TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
                                <Setter Property="Background" Value="#E74C3C"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#C0392B"/>
                                    </Trigger>
                                    <Trigger Property="IsPressed" Value="True">
                                        <Setter Property="Background" Value="#A93226"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- 功能按钮区域 -->
        <WrapPanel Grid.Row="3"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Center">
            <Button Content="关于"
                    Command="{Binding ShowAboutCommand}"
                    Style="{StaticResource ButtonStyle}"/>
            <Button Content="设置"
                    Command="{Binding ShowSettingsCommand}"
                    Style="{StaticResource ButtonStyle}"/>
            <Button Content="输入测试"
                    Command="{Binding TestInputDialogCommand}"
                    Style="{StaticResource ButtonStyle}"/>
            <Button Content="刷新"
                    Command="{Binding RefreshCommand}"
                    Style="{StaticResource ButtonStyle}"
                    Background="#27AE60">
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
                        <Setter Property="Background" Value="#27AE60"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#229954"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1E8449"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>
        </WrapPanel>

        <!-- 状态栏 -->
        <Border Grid.Row="4"
                Background="#34495E"
                CornerRadius="5"
                Padding="15,10"
                Margin="0,20,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                           Text="{Binding StatusMessage}"
                           Foreground="White"
                           VerticalAlignment="Center"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <ProgressBar Width="100"
                                 Height="15"
                                 IsIndeterminate="{Binding IsBusy}"
                                 Margin="10,0">
                        <ProgressBar.Style>
                            <Style TargetType="ProgressBar">
                                <Setter Property="Visibility" Value="Collapsed"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsBusy}" Value="True">
                                        <Setter Property="Visibility" Value="Visible"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </ProgressBar.Style>
                    </ProgressBar>
                    <Button Content="清除"
                            Command="{Binding ClearStatusCommand}"
                            Background="Transparent"
                            Foreground="White"
                            BorderThickness="1"
                            BorderBrush="White"
                            Padding="8,4"
                            FontSize="12"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 版权信息 -->
        <TextBlock Grid.Row="5"
                   Text="© 2025 AirMonitor - 基于WPF和MVVM的现代化应用程序框架"
                   HorizontalAlignment="Center"
                   FontSize="12"
                   Foreground="#7F8C8D"
                   Margin="0,10,0,0"/>
    </Grid>
</Window>
