{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "File": {"Path": "Logs/app-.log", "RollingInterval": "Day", "RetainedFileCountLimit": 30, "FileSizeLimitBytes": 10485760, "RollOnFileSizeLimit": true}}, "Application": {"Name": "AirMonitor", "Version": "1.0.0", "Environment": "Development"}, "Database": {"ConnectionString": "Data Source=airmonitor.db", "Provider": "SQLite"}, "UI": {"Theme": "Light", "Language": "zh-CN", "AutoSave": true, "AutoSaveInterval": 300}}