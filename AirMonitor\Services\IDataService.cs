namespace AirMonitor.Services;

/// <summary>
/// 数据服务接口
/// </summary>
/// <typeparam name="T">数据实体类型</typeparam>
public interface IDataService<T> where T : class
{
    /// <summary>
    /// 获取所有数据
    /// </summary>
    /// <returns>数据列表</returns>
    Task<IEnumerable<T>> GetAllAsync();

    /// <summary>
    /// 根据ID获取数据
    /// </summary>
    /// <param name="id">数据ID</param>
    /// <returns>数据实体</returns>
    Task<T?> GetByIdAsync(object id);

    /// <summary>
    /// 添加数据
    /// </summary>
    /// <param name="entity">要添加的数据实体</param>
    /// <returns>添加后的数据实体</returns>
    Task<T> AddAsync(T entity);

    /// <summary>
    /// 更新数据
    /// </summary>
    /// <param name="entity">要更新的数据实体</param>
    /// <returns>更新后的数据实体</returns>
    Task<T> UpdateAsync(T entity);

    /// <summary>
    /// 删除数据
    /// </summary>
    /// <param name="id">要删除的数据ID</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteAsync(object id);

    /// <summary>
    /// 删除数据
    /// </summary>
    /// <param name="entity">要删除的数据实体</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteAsync(T entity);

    /// <summary>
    /// 检查数据是否存在
    /// </summary>
    /// <param name="id">数据ID</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsAsync(object id);

    /// <summary>
    /// 获取数据总数
    /// </summary>
    /// <returns>数据总数</returns>
    Task<int> CountAsync();

    /// <summary>
    /// 分页获取数据
    /// </summary>
    /// <param name="pageIndex">页索引（从0开始）</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>分页数据结果</returns>
    Task<PagedResult<T>> GetPagedAsync(int pageIndex, int pageSize);

    /// <summary>
    /// 根据条件搜索数据
    /// </summary>
    /// <param name="predicate">搜索条件</param>
    /// <returns>符合条件的数据列表</returns>
    Task<IEnumerable<T>> SearchAsync(Func<T, bool> predicate);
}

/// <summary>
/// 分页结果
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class PagedResult<T>
{
    /// <summary>
    /// 数据列表
    /// </summary>
    public IEnumerable<T> Items { get; set; } = Enumerable.Empty<T>();

    /// <summary>
    /// 总记录数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 页索引（从0开始）
    /// </summary>
    public int PageIndex { get; set; }

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// 总页数
    /// </summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

    /// <summary>
    /// 是否有上一页
    /// </summary>
    public bool HasPreviousPage => PageIndex > 0;

    /// <summary>
    /// 是否有下一页
    /// </summary>
    public bool HasNextPage => PageIndex < TotalPages - 1;
}
