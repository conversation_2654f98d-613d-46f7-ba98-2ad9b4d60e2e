using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using System.IO;
using System.Windows;

namespace AirMonitor.Services;

/// <summary>
/// 对话框服务实现
/// </summary>
public class DialogService : IDialogService
{
    private readonly ILogger<DialogService> _logger;

    public DialogService(ILogger<DialogService> logger)
    {
        _logger = logger;
    }

    public Task ShowInfoAsync(string message, string title = "信息")
    {
        _logger.LogInformation("显示信息对话框: {Title} - {Message}", title, message);
        MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        return Task.CompletedTask;
    }

    public Task ShowWarningAsync(string message, string title = "警告")
    {
        _logger.LogWarning("显示警告对话框: {Title} - {Message}", title, message);
        MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
        return Task.CompletedTask;
    }

    public Task ShowErrorAsync(string message, string title = "错误")
    {
        _logger.LogError("显示错误对话框: {Title} - {Message}", title, message);
        MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
        return Task.CompletedTask;
    }

    public Task<bool> ShowConfirmAsync(string message, string title = "确认")
    {
        _logger.LogInformation("显示确认对话框: {Title} - {Message}", title, message);
        var result = MessageBox.Show(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question);
        return Task.FromResult(result == MessageBoxResult.Yes);
    }

    public Task<string?> ShowInputAsync(string message, string title = "输入", string defaultValue = "")
    {
        _logger.LogInformation("显示输入对话框: {Title} - {Message}", title, message);
        
        // 这里使用简单的InputBox实现，实际项目中可能需要自定义对话框
        var inputDialog = new InputDialog(message, title, defaultValue);
        var result = inputDialog.ShowDialog();
        
        return Task.FromResult(result == true ? inputDialog.InputText : null);
    }

    public Task<string?> ShowOpenFileDialogAsync(string filter = "所有文件|*.*", string title = "选择文件")
    {
        _logger.LogInformation("显示文件选择对话框: {Title}", title);
        
        var dialog = new OpenFileDialog
        {
            Title = title,
            Filter = filter,
            CheckFileExists = true,
            CheckPathExists = true
        };

        var result = dialog.ShowDialog();
        return Task.FromResult(result == true ? dialog.FileName : null);
    }

    public Task<string?> ShowSaveFileDialogAsync(string filter = "所有文件|*.*", string title = "保存文件", string defaultFileName = "")
    {
        _logger.LogInformation("显示文件保存对话框: {Title}", title);
        
        var dialog = new SaveFileDialog
        {
            Title = title,
            Filter = filter,
            FileName = defaultFileName,
            CheckPathExists = true
        };

        var result = dialog.ShowDialog();
        return Task.FromResult(result == true ? dialog.FileName : null);
    }

    public Task<string?> ShowFolderDialogAsync(string title = "选择文件夹")
    {
        _logger.LogInformation("显示文件夹选择对话框: {Title}", title);

        // 使用WPF的OpenFileDialog来模拟文件夹选择
        // 在实际项目中，可以使用第三方库如Ookii.Dialogs.Wpf
        var dialog = new OpenFileDialog
        {
            Title = title,
            CheckFileExists = false,
            CheckPathExists = true,
            FileName = "选择文件夹",
            Filter = "文件夹|*.folder"
        };

        var result = dialog.ShowDialog();
        if (result == true)
        {
            return Task.FromResult<string?>(Path.GetDirectoryName(dialog.FileName));
        }

        return Task.FromResult<string?>(null);
    }
}

/// <summary>
/// 简单的输入对话框
/// </summary>
public partial class InputDialog : Window
{
    public string InputText { get; private set; } = string.Empty;

    public InputDialog(string message, string title, string defaultValue = "")
    {
        InitializeComponent();
        Title = title;
        MessageTextBlock.Text = message;
        InputTextBox.Text = defaultValue;
        InputText = defaultValue;
    }

    private void OkButton_Click(object sender, RoutedEventArgs e)
    {
        InputText = InputTextBox.Text;
        DialogResult = true;
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
    }

    private void InputTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
    {
        InputText = InputTextBox.Text;
    }
}
