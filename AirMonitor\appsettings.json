{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "AirMonitor": "Debug"}, "File": {"Path": "Logs/airmonitor-.log", "RollingInterval": "Day", "RetainedFileCountLimit": 7, "FileSizeLimitBytes": 5242880, "RollOnFileSizeLimit": true}}, "Application": {"Name": "AirMonitor", "Version": "1.0.0", "Environment": "Development"}, "Database": {"ConnectionString": "Data Source=airmonitor.db", "Provider": "SQLite"}, "UI": {"Theme": "Light", "Language": "zh-CN", "AutoSave": true, "AutoSaveInterval": 300}}