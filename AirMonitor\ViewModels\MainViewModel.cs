using AirMonitor.Core;
using AirMonitor.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;

namespace AirMonitor.ViewModels;

/// <summary>
/// 主窗口ViewModel
/// </summary>
public partial class MainViewModel : BaseViewModel
{
    private readonly IDialogService _dialogService;
    private readonly IConfigurationService _configurationService;

    public MainViewModel(
        ILogger<MainViewModel> logger,
        IDialogService dialogService,
        IConfigurationService configurationService) : base(logger)
    {
        _dialogService = dialogService;
        _configurationService = configurationService;
        
        Title = "AirMonitor - 空气质量监测系统";
        WelcomeMessage = $"欢迎使用 {_configurationService.AppSettings.Application.Name} v{_configurationService.AppSettings.Application.Version}";
        
        _logger.LogInformation("MainViewModel 已初始化");
    }

    [ObservableProperty]
    private string _welcomeMessage = string.Empty;

    [ObservableProperty]
    private string _currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

    [ObservableProperty]
    private int _counter = 0;

    /// <summary>
    /// 显示关于对话框命令
    /// </summary>
    [RelayCommand]
    private async Task ShowAboutAsync()
    {
        await ExecuteAsync(async () =>
        {
            var appSettings = _configurationService.AppSettings.Application;
            var message = $"{appSettings.Name}\n版本: {appSettings.Version}\n环境: {appSettings.Environment}\n\n这是一个基于WPF和MVVM模式的现代化应用程序框架示例。";
            await _dialogService.ShowInfoAsync(message, "关于");
        }, "显示关于信息...");
    }

    /// <summary>
    /// 显示设置对话框命令
    /// </summary>
    [RelayCommand]
    private async Task ShowSettingsAsync()
    {
        await ExecuteAsync(async () =>
        {
            var result = await _dialogService.ShowConfirmAsync("是否要打开设置窗口？", "设置");
            if (result)
            {
                await _dialogService.ShowInfoAsync("设置功能尚未实现，这里只是演示对话框服务的使用。", "设置");
            }
        }, "打开设置...");
    }

    /// <summary>
    /// 计数器增加命令
    /// </summary>
    [RelayCommand]
    private void IncrementCounter()
    {
        Counter++;
        StatusMessage = $"计数器已增加到 {Counter}";
        _logger.LogInformation("计数器增加到 {Counter}", Counter);
    }

    /// <summary>
    /// 重置计数器命令
    /// </summary>
    [RelayCommand]
    private async Task ResetCounterAsync()
    {
        if (Counter == 0)
        {
            await _dialogService.ShowInfoAsync("计数器已经是0了！", "提示");
            return;
        }

        var result = await _dialogService.ShowConfirmAsync($"确定要重置计数器吗？当前值：{Counter}", "确认重置");
        if (result)
        {
            Counter = 0;
            StatusMessage = "计数器已重置";
            _logger.LogInformation("计数器已重置");
        }
    }

    /// <summary>
    /// 测试输入对话框命令
    /// </summary>
    [RelayCommand]
    private async Task TestInputDialogAsync()
    {
        await ExecuteAsync(async () =>
        {
            var input = await _dialogService.ShowInputAsync("请输入您的姓名：", "输入测试", "张三");
            if (!string.IsNullOrEmpty(input))
            {
                await _dialogService.ShowInfoAsync($"您好，{input}！", "问候");
            }
        }, "显示输入对话框...");
    }

    /// <summary>
    /// 更新时间
    /// </summary>
    public void UpdateTime()
    {
        CurrentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
    }

    /// <summary>
    /// 重写刷新方法
    /// </summary>
    protected override async Task OnRefreshAsync()
    {
        await ExecuteAsync(async () =>
        {
            UpdateTime();
            await Task.Delay(500); // 模拟刷新操作
            StatusMessage = "数据已刷新";
        }, "刷新数据中...", "数据刷新完成", true);
    }
}
